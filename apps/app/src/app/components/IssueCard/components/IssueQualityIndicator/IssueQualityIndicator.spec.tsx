import { render, screen } from 'tests/test-utils';
import { IssueQualityIndicator } from '.';

jest.mock('@shape-construction/hooks', () => ({
  useMediaQuery: () => true,
}));

describe('IssueQualityIndicator', () => {
  it('renders nothing when quality score is null', () => {
    const { container } = render(<IssueQualityIndicator qualityScore={null} />);

    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  it('renders nothing when quality score is >= 30', () => {
    const { container } = render(<IssueQualityIndicator qualityScore={35} />);

    expect(container.firstChild).toHaveClass('');
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  it('renders progress bar for low quality score', () => {
    render(<IssueQualityIndicator qualityScore={10} />);

    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.getByLabelText(/Issue quality/)).toBeInTheDocument();
  });

  it('renders progress bar for medium quality score', () => {
    render(<IssueQualityIndicator qualityScore={25} />);

    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.getByLabelText(/Issue quality/)).toBeInTheDocument();
  });

  it('opens popover on click', async () => {
    const { user } = render(<IssueQualityIndicator qualityScore={15} />);

    await user.click(screen.getByRole('button'));

    expect(screen.getByText('mocked.notUseful.title')).toBeInTheDocument();
    expect(screen.getByText('mocked.notUseful.scoreRange')).toBeInTheDocument();
    expect(screen.getByText('mocked.notUseful.description')).toBeInTheDocument();
  });

  it('shows correct messages for "the basics" category', async () => {
    const { user } = render(<IssueQualityIndicator qualityScore={22} />);

    await user.click(screen.getByRole('button'));

    expect(screen.getByText('mocked.theBasics.title')).toBeInTheDocument();
    expect(screen.getByText('mocked.theBasics.scoreRange')).toBeInTheDocument();
    expect(screen.getByText('mocked.theBasics.description')).toBeInTheDocument();
  });
});